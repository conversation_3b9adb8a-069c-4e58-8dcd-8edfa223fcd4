"""
安全特性模块

提供完整的安全保护功能，包括：
- JWT签名验证
- 请求频率限制
- 审计日志记录
- 安全头部设置
- 异常活动检测
"""

import asyncio
import hashlib
import time
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Any, Tuple
from uuid import UUID

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from ai_gen_hub.auth.exceptions import (
    RateLimitError,
    SecurityPolicyViolationError,
    SecurityError,
)
from ai_gen_hub.auth.models import AuthUser, Permission
from ai_gen_hub.core.logging import LoggerMixin


class RateLimiter(LoggerMixin):
    """频率限制器
    
    实现基于令牌桶算法的频率限制，支持：
    - 全局频率限制
    - 用户级频率限制
    - IP级频率限制
    - 动态限制调整
    """
    
    def __init__(
        self,
        default_requests_per_minute: int = 100,
        default_burst_size: int = 10,
        cleanup_interval: int = 300  # 5分钟清理一次
    ):
        super().__init__()
        self.default_requests_per_minute = default_requests_per_minute
        self.default_burst_size = default_burst_size
        self.cleanup_interval = cleanup_interval
        
        # 存储不同类型的限制器
        self._user_limiters: Dict[UUID, Dict[str, Any]] = {}
        self._ip_limiters: Dict[str, Dict[str, Any]] = {}
        self._global_limiter: Dict[str, Any] = self._create_limiter_state(
            default_requests_per_minute, default_burst_size
        )
        
        # 最后清理时间
        self._last_cleanup = time.time()
    
    def _create_limiter_state(
        self, 
        requests_per_minute: int, 
        burst_size: int
    ) -> Dict[str, Any]:
        """创建限制器状态"""
        return {
            "requests_per_minute": requests_per_minute,
            "burst_size": burst_size,
            "tokens": burst_size,
            "last_refill": time.time(),
            "request_count": 0,
            "last_request": time.time()
        }
    
    def _refill_tokens(self, limiter_state: Dict[str, Any]) -> None:
        """补充令牌"""
        now = time.time()
        time_passed = now - limiter_state["last_refill"]
        
        # 计算应该补充的令牌数
        tokens_to_add = time_passed * (limiter_state["requests_per_minute"] / 60.0)
        
        # 更新令牌数，不超过桶容量
        limiter_state["tokens"] = min(
            limiter_state["burst_size"],
            limiter_state["tokens"] + tokens_to_add
        )
        limiter_state["last_refill"] = now
    
    def check_rate_limit(
        self,
        identifier: str,
        identifier_type: str = "global",
        custom_limit: Optional[int] = None,
        custom_burst: Optional[int] = None
    ) -> Tuple[bool, Dict[str, Any]]:
        """检查频率限制
        
        Args:
            identifier: 标识符（用户ID、IP地址等）
            identifier_type: 标识符类型（user、ip、global）
            custom_limit: 自定义限制（每分钟请求数）
            custom_burst: 自定义突发大小
            
        Returns:
            (是否允许, 限制信息) 元组
        """
        # 选择对应的限制器存储
        if identifier_type == "user":
            limiters = self._user_limiters
        elif identifier_type == "ip":
            limiters = self._ip_limiters
        else:
            # 全局限制器
            limiter_state = self._global_limiter
            self._refill_tokens(limiter_state)
            
            if limiter_state["tokens"] >= 1:
                limiter_state["tokens"] -= 1
                limiter_state["request_count"] += 1
                limiter_state["last_request"] = time.time()
                return True, self._get_limit_info(limiter_state)
            else:
                return False, self._get_limit_info(limiter_state)
        
        # 获取或创建限制器状态
        if identifier not in limiters:
            limiters[identifier] = self._create_limiter_state(
                custom_limit or self.default_requests_per_minute,
                custom_burst or self.default_burst_size
            )
        
        limiter_state = limiters[identifier]
        
        # 如果提供了自定义限制，更新限制器
        if custom_limit:
            limiter_state["requests_per_minute"] = custom_limit
        if custom_burst:
            limiter_state["burst_size"] = custom_burst
        
        # 补充令牌
        self._refill_tokens(limiter_state)
        
        # 检查是否有可用令牌
        if limiter_state["tokens"] >= 1:
            limiter_state["tokens"] -= 1
            limiter_state["request_count"] += 1
            limiter_state["last_request"] = time.time()
            
            self.logger.debug(
                "频率限制检查通过",
                identifier=identifier,
                identifier_type=identifier_type,
                remaining_tokens=limiter_state["tokens"]
            )
            
            return True, self._get_limit_info(limiter_state)
        else:
            self.logger.warning(
                "频率限制超出",
                identifier=identifier,
                identifier_type=identifier_type,
                requests_per_minute=limiter_state["requests_per_minute"]
            )
            
            return False, self._get_limit_info(limiter_state)
    
    def _get_limit_info(self, limiter_state: Dict[str, Any]) -> Dict[str, Any]:
        """获取限制信息"""
        return {
            "requests_per_minute": limiter_state["requests_per_minute"],
            "burst_size": limiter_state["burst_size"],
            "remaining_tokens": int(limiter_state["tokens"]),
            "request_count": limiter_state["request_count"],
            "reset_time": limiter_state["last_refill"] + 60  # 下次重置时间
        }
    
    def get_user_limit_info(self, user_id: UUID) -> Optional[Dict[str, Any]]:
        """获取用户限制信息"""
        user_key = str(user_id)
        if user_key in self._user_limiters:
            return self._get_limit_info(self._user_limiters[user_key])
        return None
    
    def set_user_limit(
        self,
        user_id: UUID,
        requests_per_minute: int,
        burst_size: Optional[int] = None
    ) -> None:
        """设置用户自定义限制"""
        user_key = str(user_id)
        if user_key not in self._user_limiters:
            self._user_limiters[user_key] = self._create_limiter_state(
                requests_per_minute,
                burst_size or self.default_burst_size
            )
        else:
            limiter_state = self._user_limiters[user_key]
            limiter_state["requests_per_minute"] = requests_per_minute
            if burst_size:
                limiter_state["burst_size"] = burst_size
        
        self.logger.info(
            "用户频率限制设置成功",
            user_id=str(user_id),
            requests_per_minute=requests_per_minute,
            burst_size=burst_size
        )
    
    def cleanup_old_limiters(self) -> None:
        """清理旧的限制器状态"""
        now = time.time()
        
        # 只在间隔时间后执行清理
        if now - self._last_cleanup < self.cleanup_interval:
            return
        
        cutoff_time = now - 3600  # 1小时前
        
        # 清理用户限制器
        expired_users = [
            user_id for user_id, state in self._user_limiters.items()
            if state["last_request"] < cutoff_time
        ]
        for user_id in expired_users:
            del self._user_limiters[user_id]
        
        # 清理IP限制器
        expired_ips = [
            ip for ip, state in self._ip_limiters.items()
            if state["last_request"] < cutoff_time
        ]
        for ip in expired_ips:
            del self._ip_limiters[ip]
        
        self._last_cleanup = now
        
        if expired_users or expired_ips:
            self.logger.info(
                "清理过期限制器",
                expired_users=len(expired_users),
                expired_ips=len(expired_ips)
            )


class AuditLogger(LoggerMixin):
    """审计日志记录器
    
    记录系统中的重要安全事件，包括：
    - 用户认证事件
    - 权限检查事件
    - 敏感操作事件
    - 异常活动事件
    """
    
    def __init__(self, retention_days: int = 90):
        super().__init__()
        self.retention_days = retention_days
        self._audit_logs: List[Dict[str, Any]] = []
        self._max_logs_in_memory = 10000
    
    def log_authentication_event(
        self,
        user_id: Optional[UUID],
        event_type: str,
        success: bool,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        """记录认证事件
        
        Args:
            user_id: 用户ID
            event_type: 事件类型（login、logout、token_refresh等）
            success: 是否成功
            ip_address: IP地址
            user_agent: 用户代理
            details: 额外详情
        """
        self._log_audit_event(
            category="authentication",
            event_type=event_type,
            user_id=user_id,
            success=success,
            ip_address=ip_address,
            user_agent=user_agent,
            details=details
        )
    
    def log_authorization_event(
        self,
        user_id: UUID,
        permission: str,
        resource: Optional[str],
        success: bool,
        ip_address: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        """记录授权事件
        
        Args:
            user_id: 用户ID
            permission: 权限名称
            resource: 资源标识
            success: 是否成功
            ip_address: IP地址
            details: 额外详情
        """
        self._log_audit_event(
            category="authorization",
            event_type="permission_check",
            user_id=user_id,
            success=success,
            ip_address=ip_address,
            details={
                "permission": permission,
                "resource": resource,
                **(details or {})
            }
        )
    
    def log_security_event(
        self,
        event_type: str,
        severity: str,
        user_id: Optional[UUID] = None,
        ip_address: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        """记录安全事件
        
        Args:
            event_type: 事件类型
            severity: 严重程度（low、medium、high、critical）
            user_id: 用户ID
            ip_address: IP地址
            details: 额外详情
        """
        self._log_audit_event(
            category="security",
            event_type=event_type,
            user_id=user_id,
            ip_address=ip_address,
            details={
                "severity": severity,
                **(details or {})
            }
        )
    
    def log_api_event(
        self,
        user_id: Optional[UUID],
        endpoint: str,
        method: str,
        status_code: int,
        response_time: float,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        """记录API访问事件
        
        Args:
            user_id: 用户ID
            endpoint: API端点
            method: HTTP方法
            status_code: 响应状态码
            response_time: 响应时间
            ip_address: IP地址
            user_agent: 用户代理
            details: 额外详情
        """
        self._log_audit_event(
            category="api_access",
            event_type="api_call",
            user_id=user_id,
            success=200 <= status_code < 400,
            ip_address=ip_address,
            user_agent=user_agent,
            details={
                "endpoint": endpoint,
                "method": method,
                "status_code": status_code,
                "response_time": response_time,
                **(details or {})
            }
        )
    
    def _log_audit_event(
        self,
        category: str,
        event_type: str,
        user_id: Optional[UUID] = None,
        success: Optional[bool] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        """记录审计事件"""
        audit_log = {
            "timestamp": datetime.utcnow().isoformat(),
            "category": category,
            "event_type": event_type,
            "user_id": str(user_id) if user_id else None,
            "success": success,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "details": details or {}
        }
        
        # 添加到内存日志
        self._audit_logs.append(audit_log)
        
        # 限制内存中的日志数量
        if len(self._audit_logs) > self._max_logs_in_memory:
            self._audit_logs = self._audit_logs[-self._max_logs_in_memory:]
        
        # 记录到系统日志
        self.logger.info(
            "审计事件",
            category=category,
            event_type=event_type,
            user_id=str(user_id) if user_id else None,
            success=success,
            ip_address=ip_address,
            **audit_log["details"]
        )
    
    def get_audit_logs(
        self,
        category: Optional[str] = None,
        user_id: Optional[UUID] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """获取审计日志
        
        Args:
            category: 事件类别
            user_id: 用户ID
            start_time: 开始时间
            end_time: 结束时间
            limit: 返回数量限制
            
        Returns:
            审计日志列表
        """
        filtered_logs = self._audit_logs
        
        # 按条件过滤
        if category:
            filtered_logs = [log for log in filtered_logs if log["category"] == category]
        
        if user_id:
            user_id_str = str(user_id)
            filtered_logs = [log for log in filtered_logs if log["user_id"] == user_id_str]
        
        if start_time:
            start_time_str = start_time.isoformat()
            filtered_logs = [log for log in filtered_logs if log["timestamp"] >= start_time_str]
        
        if end_time:
            end_time_str = end_time.isoformat()
            filtered_logs = [log for log in filtered_logs if log["timestamp"] <= end_time_str]
        
        # 按时间倒序排序并限制数量
        filtered_logs.sort(key=lambda x: x["timestamp"], reverse=True)
        return filtered_logs[:limit]


class SecurityHeaders:
    """安全头部管理器
    
    设置HTTP安全头部，包括：
    - Content Security Policy (CSP)
    - X-Frame-Options
    - X-Content-Type-Options
    - X-XSS-Protection
    - Strict-Transport-Security
    """
    
    @staticmethod
    def get_security_headers() -> Dict[str, str]:
        """获取安全头部"""
        return {
            # 内容安全策略
            "Content-Security-Policy": (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self' https:; "
                "connect-src 'self' https:; "
                "frame-ancestors 'none';"
            ),
            
            # 防止点击劫持
            "X-Frame-Options": "DENY",
            
            # 防止MIME类型嗅探
            "X-Content-Type-Options": "nosniff",
            
            # XSS保护
            "X-XSS-Protection": "1; mode=block",
            
            # 强制HTTPS（生产环境）
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            
            # 引用者策略
            "Referrer-Policy": "strict-origin-when-cross-origin",
            
            # 权限策略
            "Permissions-Policy": (
                "geolocation=(), "
                "microphone=(), "
                "camera=(), "
                "payment=(), "
                "usb=(), "
                "magnetometer=(), "
                "gyroscope=(), "
                "speaker=()"
            ),
        }
    
    @staticmethod
    def apply_security_headers(response: Response) -> None:
        """应用安全头部到响应"""
        headers = SecurityHeaders.get_security_headers()
        for name, value in headers.items():
            response.headers[name] = value


class SecurityManager(LoggerMixin):
    """安全管理器
    
    统一管理所有安全特性，包括：
    - 频率限制
    - 审计日志
    - 异常检测
    - 安全策略
    """
    
    def __init__(
        self,
        rate_limiter: Optional[RateLimiter] = None,
        audit_logger: Optional[AuditLogger] = None
    ):
        super().__init__()
        self.rate_limiter = rate_limiter or RateLimiter()
        self.audit_logger = audit_logger or AuditLogger()
        
        # 异常活动检测
        self._failed_attempts: Dict[str, List[float]] = defaultdict(list)
        self._suspicious_ips: Set[str] = set()
        
        # 安全策略配置
        self.max_failed_attempts = 5
        self.failed_attempts_window = 300  # 5分钟
        self.lockout_duration = 900  # 15分钟
    
    def check_request_security(
        self,
        request: Request,
        user: Optional[AuthUser] = None
    ) -> Tuple[bool, Optional[str]]:
        """检查请求安全性
        
        Args:
            request: HTTP请求
            user: 用户对象（如果已认证）
            
        Returns:
            (是否允许, 拒绝原因) 元组
        """
        client_ip = self._get_client_ip(request)
        
        # 检查IP是否被标记为可疑
        if client_ip in self._suspicious_ips:
            self.audit_logger.log_security_event(
                "blocked_suspicious_ip",
                "high",
                user_id=user.id if user else None,
                ip_address=client_ip
            )
            return False, "IP地址被标记为可疑"
        
        # 检查频率限制
        if user:
            # 用户级限制
            allowed, limit_info = self.rate_limiter.check_rate_limit(
                str(user.id), "user", user.rate_limit
            )
            if not allowed:
                self.audit_logger.log_security_event(
                    "rate_limit_exceeded",
                    "medium",
                    user_id=user.id,
                    ip_address=client_ip,
                    details=limit_info
                )
                return False, "用户请求频率超出限制"
        
        # IP级限制
        allowed, limit_info = self.rate_limiter.check_rate_limit(
            client_ip, "ip"
        )
        if not allowed:
            self.audit_logger.log_security_event(
                "ip_rate_limit_exceeded",
                "medium",
                user_id=user.id if user else None,
                ip_address=client_ip,
                details=limit_info
            )
            return False, "IP请求频率超出限制"
        
        return True, None
    
    def record_failed_authentication(
        self,
        identifier: str,
        ip_address: str,
        user_agent: Optional[str] = None
    ) -> None:
        """记录认证失败事件"""
        current_time = time.time()
        
        # 记录失败尝试
        self._failed_attempts[identifier].append(current_time)
        
        # 清理过期的失败记录
        cutoff_time = current_time - self.failed_attempts_window
        self._failed_attempts[identifier] = [
            attempt_time for attempt_time in self._failed_attempts[identifier]
            if attempt_time > cutoff_time
        ]
        
        # 检查是否超过失败阈值
        if len(self._failed_attempts[identifier]) >= self.max_failed_attempts:
            self._suspicious_ips.add(ip_address)
            
            self.audit_logger.log_security_event(
                "multiple_failed_attempts",
                "high",
                ip_address=ip_address,
                details={
                    "identifier": identifier,
                    "attempt_count": len(self._failed_attempts[identifier]),
                    "user_agent": user_agent
                }
            )
            
            self.logger.warning(
                "检测到多次认证失败",
                identifier=identifier,
                ip_address=ip_address,
                attempt_count=len(self._failed_attempts[identifier])
            )
    
    def is_account_locked(self, identifier: str) -> bool:
        """检查账户是否被锁定"""
        if identifier not in self._failed_attempts:
            return False
        
        current_time = time.time()
        cutoff_time = current_time - self.failed_attempts_window
        
        # 清理过期的失败记录
        self._failed_attempts[identifier] = [
            attempt_time for attempt_time in self._failed_attempts[identifier]
            if attempt_time > cutoff_time
        ]
        
        return len(self._failed_attempts[identifier]) >= self.max_failed_attempts
    
    def clear_failed_attempts(self, identifier: str) -> None:
        """清除失败尝试记录（成功认证后调用）"""
        if identifier in self._failed_attempts:
            del self._failed_attempts[identifier]
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头部
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 使用客户端IP
        return request.client.host if request.client else "unknown"
