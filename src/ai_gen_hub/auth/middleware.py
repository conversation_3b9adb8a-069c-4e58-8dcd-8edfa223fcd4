"""
认证和授权中间件

提供FastAPI中间件支持，包括：
- 身份认证中间件
- 权限授权中间件
- 安全保护中间件
- 审计日志中间件
"""

import time
from typing import Callable, Optional, List, Dict, Any
from uuid import UUID

from fastapi import Request, Response, HTTPException, Depends
from fastapi.security import HTTP<PERSON>earer, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from ai_gen_hub.auth.exceptions import (
    AuthenticationError,
    AuthorizationError,
    TokenError,
    RateLimitError,
    SecurityError,
    get_http_status_code,
)
from ai_gen_hub.auth.models import AuthUser, Permission, TokenScope
from ai_gen_hub.auth.tokens import APITokenManager, JWTTokenManager
from ai_gen_hub.auth.rbac import RBACManager
from ai_gen_hub.auth.security import SecurityManager, SecurityHeaders
from ai_gen_hub.core.logging import LoggerMixin


class AuthenticationMiddleware(BaseHTTPMiddleware, LoggerMixin):
    """身份认证中间件
    
    处理所有的身份认证逻辑，包括：
    - JWT令牌验证
    - API Token验证
    - 用户信息提取
    - 会话管理
    """
    
    def __init__(
        self,
        app,
        jwt_manager: JWTTokenManager,
        api_token_manager: APITokenManager,
        user_service: Any,  # 用户服务接口
        excluded_paths: Optional[List[str]] = None
    ):
        super().__init__(app)
        LoggerMixin.__init__(self)
        self.jwt_manager = jwt_manager
        self.api_token_manager = api_token_manager
        self.user_service = user_service
        self.excluded_paths = excluded_paths or [
            "/docs",
            "/redoc",
            "/openapi.json",
            "/health",
            "/auth/login",
            "/auth/callback",
            "/auth/refresh",
        ]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        start_time = time.time()
        
        # 检查是否需要认证
        if self._is_excluded_path(request.url.path):
            response = await call_next(request)
            return response
        
        try:
            # 提取和验证令牌
            user = await self._authenticate_request(request)
            
            # 将用户信息添加到请求状态
            request.state.user = user
            request.state.authenticated = True
            
            # 继续处理请求
            response = await call_next(request)
            
            # 记录成功的API访问
            self._log_api_access(request, response, user, time.time() - start_time)
            
            return response
            
        except (AuthenticationError, TokenError) as e:
            self.logger.warning(
                "认证失败",
                path=request.url.path,
                error=str(e),
                ip_address=self._get_client_ip(request)
            )
            
            return JSONResponse(
                status_code=get_http_status_code(e),
                content={
                    "error": "authentication_failed",
                    "message": str(e),
                    "error_code": e.error_code
                }
            )
        
        except Exception as e:
            self.logger.error(
                "认证中间件异常",
                path=request.url.path,
                error=str(e),
                error_type=type(e).__name__
            )
            
            return JSONResponse(
                status_code=500,
                content={
                    "error": "internal_error",
                    "message": "内部服务器错误"
                }
            )
    
    def _is_excluded_path(self, path: str) -> bool:
        """检查路径是否被排除在认证之外"""
        return any(path.startswith(excluded) for excluded in self.excluded_paths)
    
    async def _authenticate_request(self, request: Request) -> AuthUser:
        """认证请求并返回用户信息"""
        # 提取Authorization头部
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            raise AuthenticationError("缺少Authorization头部")
        
        # 解析令牌
        if auth_header.startswith("Bearer "):
            token = auth_header[7:]
            
            # 尝试JWT令牌验证
            try:
                payload = self.jwt_manager.verify_token(token)
                user_id = UUID(payload["sub"])
                user = await self.user_service.get_user_by_id(user_id)
                
                if not user:
                    raise AuthenticationError("用户不存在")
                
                if not user.is_active():
                    raise AuthenticationError("用户账户已禁用")
                
                return user
                
            except TokenError:
                # JWT验证失败，尝试API Token
                pass
        
        # 尝试API Token验证
        if auth_header.startswith("Bearer ak_"):
            token = auth_header[7:]
            api_token = self.api_token_manager.verify_api_token(token)
            
            if not api_token:
                raise AuthenticationError("无效的API Token")
            
            user = await self.user_service.get_user_by_id(api_token.user_id)
            if not user:
                raise AuthenticationError("API Token关联的用户不存在")
            
            if not user.is_active():
                raise AuthenticationError("用户账户已禁用")
            
            # 将API Token信息添加到用户对象
            user.metadata["api_token"] = {
                "id": str(api_token.id),
                "name": api_token.name,
                "scopes": list(api_token.scopes),
                "permissions": list(api_token.permissions)
            }
            
            return user
        
        raise AuthenticationError("无效的认证令牌格式")
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
    
    def _log_api_access(
        self,
        request: Request,
        response: Response,
        user: AuthUser,
        response_time: float
    ) -> None:
        """记录API访问日志"""
        self.logger.info(
            "API访问",
            user_id=str(user.id),
            username=user.username,
            method=request.method,
            path=request.url.path,
            status_code=response.status_code,
            response_time=f"{response_time:.3f}s",
            ip_address=self._get_client_ip(request),
            user_agent=request.headers.get("User-Agent")
        )


class AuthorizationMiddleware(BaseHTTPMiddleware, LoggerMixin):
    """权限授权中间件
    
    处理权限检查逻辑，包括：
    - 基于路径的权限检查
    - RBAC权限验证
    - 资源级访问控制
    """
    
    def __init__(
        self,
        app,
        rbac_manager: RBACManager,
        path_permissions: Optional[Dict[str, List[Permission]]] = None
    ):
        super().__init__(app)
        LoggerMixin.__init__(self)
        self.rbac_manager = rbac_manager
        self.path_permissions = path_permissions or {}
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        # 检查是否已认证
        if not getattr(request.state, "authenticated", False):
            response = await call_next(request)
            return response
        
        user = getattr(request.state, "user", None)
        if not user:
            response = await call_next(request)
            return response
        
        try:
            # 检查路径权限
            await self._check_path_permissions(request, user)
            
            # 继续处理请求
            response = await call_next(request)
            return response
            
        except AuthorizationError as e:
            self.logger.warning(
                "授权失败",
                user_id=str(user.id),
                username=user.username,
                path=request.url.path,
                error=str(e)
            )
            
            return JSONResponse(
                status_code=get_http_status_code(e),
                content={
                    "error": "authorization_failed",
                    "message": str(e),
                    "error_code": e.error_code
                }
            )
        
        except Exception as e:
            self.logger.error(
                "授权中间件异常",
                user_id=str(user.id),
                path=request.url.path,
                error=str(e),
                error_type=type(e).__name__
            )
            
            return JSONResponse(
                status_code=500,
                content={
                    "error": "internal_error",
                    "message": "内部服务器错误"
                }
            )
    
    async def _check_path_permissions(self, request: Request, user: AuthUser) -> None:
        """检查路径权限"""
        path = request.url.path
        method = request.method
        
        # 查找匹配的路径权限配置
        required_permissions = self._get_required_permissions(path, method)
        
        if not required_permissions:
            # 没有特定权限要求，允许访问
            return
        
        # 检查用户是否有所需权限
        user_permissions = self.rbac_manager.get_user_permissions(user.id)
        
        # 检查是否有任一所需权限
        has_permission = any(
            perm in user_permissions for perm in required_permissions
        )
        
        if not has_permission:
            raise AuthorizationError(
                f"访问 {path} 需要以下权限之一: {[p.value for p in required_permissions]}"
            )
        
        self.logger.debug(
            "权限检查通过",
            user_id=str(user.id),
            path=path,
            method=method,
            required_permissions=[p.value for p in required_permissions]
        )
    
    def _get_required_permissions(self, path: str, method: str) -> List[Permission]:
        """获取路径所需的权限"""
        # 检查精确匹配
        key = f"{method} {path}"
        if key in self.path_permissions:
            return self.path_permissions[key]
        
        # 检查路径前缀匹配
        for pattern, permissions in self.path_permissions.items():
            if " " in pattern:
                pattern_method, pattern_path = pattern.split(" ", 1)
                if pattern_method == method and path.startswith(pattern_path):
                    return permissions
            else:
                # 只有路径，不限制方法
                if path.startswith(pattern):
                    return self.path_permissions[pattern]
        
        return []


class SecurityMiddleware(BaseHTTPMiddleware, LoggerMixin):
    """安全保护中间件
    
    提供安全保护功能，包括：
    - 频率限制
    - 安全头部设置
    - 异常检测
    - 审计日志
    """
    
    def __init__(
        self,
        app,
        security_manager: SecurityManager
    ):
        super().__init__(app)
        LoggerMixin.__init__(self)
        self.security_manager = security_manager
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        start_time = time.time()
        user = getattr(request.state, "user", None)
        
        try:
            # 检查请求安全性
            allowed, reason = self.security_manager.check_request_security(request, user)
            
            if not allowed:
                self.logger.warning(
                    "请求被安全策略拒绝",
                    reason=reason,
                    ip_address=self._get_client_ip(request),
                    user_id=str(user.id) if user else None
                )
                
                return JSONResponse(
                    status_code=429 if "频率" in reason else 403,
                    content={
                        "error": "security_policy_violation",
                        "message": reason
                    }
                )
            
            # 继续处理请求
            response = await call_next(request)
            
            # 设置安全头部
            SecurityHeaders.apply_security_headers(response)
            
            # 记录API访问审计日志
            self.security_manager.audit_logger.log_api_event(
                user_id=user.id if user else None,
                endpoint=request.url.path,
                method=request.method,
                status_code=response.status_code,
                response_time=time.time() - start_time,
                ip_address=self._get_client_ip(request),
                user_agent=request.headers.get("User-Agent")
            )
            
            return response
            
        except RateLimitError as e:
            return JSONResponse(
                status_code=429,
                content={
                    "error": "rate_limit_exceeded",
                    "message": str(e),
                    "retry_after": getattr(e, "retry_after", 60)
                }
            )
        
        except SecurityError as e:
            return JSONResponse(
                status_code=get_http_status_code(e),
                content={
                    "error": "security_error",
                    "message": str(e)
                }
            )
        
        except Exception as e:
            self.logger.error(
                "安全中间件异常",
                error=str(e),
                error_type=type(e).__name__,
                path=request.url.path
            )
            
            return JSONResponse(
                status_code=500,
                content={
                    "error": "internal_error",
                    "message": "内部服务器错误"
                }
            )
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"


# FastAPI依赖项
class AuthDependency:
    """认证依赖项
    
    用于FastAPI路由的认证依赖注入
    """
    
    def __init__(self, required_permissions: Optional[List[Permission]] = None):
        self.required_permissions = required_permissions or []
        self.security = HTTPBearer()
    
    async def __call__(
        self,
        request: Request,
        credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())
    ) -> AuthUser:
        """认证依赖项调用"""
        # 从请求状态获取用户（由中间件设置）
        user = getattr(request.state, "user", None)
        
        if not user:
            raise HTTPException(
                status_code=401,
                detail="未认证的请求"
            )
        
        # 检查所需权限
        if self.required_permissions:
            # 这里需要访问RBAC管理器，可以通过依赖注入获取
            # 简化实现：检查用户权限
            user_permissions = user.permissions
            has_permission = any(
                perm in user_permissions for perm in self.required_permissions
            )
            
            if not has_permission:
                raise HTTPException(
                    status_code=403,
                    detail=f"需要以下权限之一: {[p.value for p in self.required_permissions]}"
                )
        
        return user


# 便捷的依赖项创建函数
def require_auth(permissions: Optional[List[Permission]] = None) -> AuthDependency:
    """创建认证依赖项
    
    Args:
        permissions: 所需权限列表
        
    Returns:
        认证依赖项
    """
    return AuthDependency(permissions)


def require_permissions(*permissions: Permission) -> AuthDependency:
    """创建权限依赖项
    
    Args:
        permissions: 所需权限
        
    Returns:
        认证依赖项
    """
    return AuthDependency(list(permissions))
