# 自定义 AI Provider 接入指南

本指南详细介绍如何在 AI Gen Hub 中接入自定义的 AI provider，包括完整的实现步骤、配置方法和测试验证。

## 📋 目录

- [概述](#概述)
- [实现步骤](#实现步骤)
- [创建自定义 Provider](#创建自定义-provider)
- [配置系统集成](#配置系统集成)
- [注册和使用](#注册和使用)
- [测试验证](#测试验证)
- [最佳实践](#最佳实践)

## 🌟 概述

### 架构说明

AI Gen Hub 采用插件化的 provider 架构，支持轻松接入新的 AI 服务商：

```
BaseProvider (基类)
├── 通用功能：HTTP客户端、错误处理、重试机制
├── 抽象方法：需要子类实现的核心功能
└── 接口规范：统一的请求/响应格式

CustomProvider (自定义实现)
├── 继承 BaseProvider
├── 实现抽象方法
├── 定义支持的模型和功能
└── 处理特定 API 格式
```

### 核心组件

- **BaseProvider**: 提供通用功能的基类
- **ProviderConfig**: 配置管理
- **ProviderManager**: 供应商生命周期管理
- **KeyManager**: API 密钥安全管理
- **接口规范**: 统一的请求/响应格式

## 🚀 实现步骤

### 步骤 1: 创建 Provider 类

继承 `BaseProvider` 并实现必要的抽象方法：

```python
from ai_gen_hub.providers.base import BaseProvider
from ai_gen_hub.core.interfaces import (
    TextGenerationRequest,
    TextGenerationResponse,
    ModelType
)

class CustomProvider(BaseProvider):
    """自定义 AI Provider 实现"""
    
    def __init__(self, config: ProviderConfig, key_manager: KeyManager):
        super().__init__("custom_provider", config, key_manager)
        
        # 设置 API 基础 URL
        self.base_url = config.base_url or "https://api.custom-ai.com/v1"
        
        # 定义支持的模型类型
        self._supported_model_types = [
            ModelType.TEXT_GENERATION,
            # ModelType.IMAGE_GENERATION,  # 如果支持图像生成
        ]
        
        # 定义支持的模型列表
        self._supported_models = [
            "custom-model-v1",
            "custom-model-v2",
            "custom-model-lite",
        ]
```

### 步骤 2: 实现核心方法

必须实现的抽象方法：

```python
async def _perform_health_check(self, api_key: str) -> bool:
    """执行健康检查"""
    try:
        headers = {"Authorization": f"Bearer {api_key}"}
        response = await self._client.get(
            f"{self.base_url}/models",
            headers=headers
        )
        return response.status_code == 200
    except Exception:
        return False

async def generate_text(self, request: TextGenerationRequest) -> TextGenerationResponse:
    """生成文本"""
    # 获取 API 密钥
    api_key = await self.key_manager.get_key(self.name)
    if not api_key:
        raise AuthenticationError("未找到有效的 API 密钥")
    
    # 构建请求数据
    request_data = self._build_request(request)
    
    # 发送请求
    headers = {"Authorization": f"Bearer {api_key.key}"}
    response = await self._make_request(
        "POST", 
        f"{self.base_url}/chat/completions",
        headers=headers,
        json_data=request_data
    )
    
    # 解析响应
    return self._parse_response(response.json())
```

### 步骤 3: 配置系统集成

在 `src/ai_gen_hub/config/settings.py` 中添加配置支持：

```python
class Settings(BaseSettings):
    # 现有配置...
    
    # 添加自定义 provider 配置
    custom_provider: ProviderConfig = Field(default_factory=ProviderConfig)
    
    def _build_provider_configs(self):
        # 现有配置构建...
        
        # 添加自定义 provider 配置构建
        self.custom_provider = self._build_single_provider_config(
            "custom_provider",
            self.custom_provider_api_keys,
            self.custom_provider_base_url,
            self.custom_provider_timeout,
            self.custom_provider_max_retries,
            self.custom_provider_enabled
        )
```

## 📝 完整示例

### 步骤 4: 注册到 Provider Manager

在 `src/ai_gen_hub/services/provider_manager.py` 中添加初始化逻辑：

```python
async def _initialize_providers(self) -> None:
    """初始化所有配置的供应商"""
    # 现有供应商初始化...

    # 自定义 Provider
    if self.settings.custom_provider.enabled and self.settings.custom_provider.api_keys:
        try:
            provider = CustomProvider(self.settings.custom_provider, self.key_manager)
            await provider.initialize()
            self.providers["custom_provider"] = provider
            self.logger.info("自定义供应商初始化成功")
        except Exception as e:
            self.logger.error("自定义供应商初始化失败", error=str(e))
```

### 步骤 5: 环境变量配置

设置环境变量以配置自定义 provider：

```bash
# 基础配置
export CUSTOM_PROVIDER_ENABLED=true
export CUSTOM_PROVIDER_API_KEYS="your-api-key-1,your-api-key-2"
export CUSTOM_PROVIDER_BASE_URL="https://api.custom-ai.com/v1"
export CUSTOM_PROVIDER_TIMEOUT=120
export CUSTOM_PROVIDER_MAX_RETRIES=3

# 可选配置
export CUSTOM_PROVIDER_RATE_LIMIT=100  # 每分钟请求数
```

## 🔧 配置系统集成

### 修改配置类

需要在配置系统中添加对新 provider 的支持：

```python
# src/ai_gen_hub/config/settings.py

class Settings(BaseSettings):
    # 自定义 Provider 环境变量
    custom_provider_api_keys: List[str] = Field(
        default_factory=list,
        env="CUSTOM_PROVIDER_API_KEYS"
    )
    custom_provider_base_url: Optional[str] = Field(
        None,
        env="CUSTOM_PROVIDER_BASE_URL"
    )
    custom_provider_enabled: bool = Field(
        True,
        env="CUSTOM_PROVIDER_ENABLED"
    )
    custom_provider_timeout: int = Field(
        120,
        env="CUSTOM_PROVIDER_TIMEOUT"
    )
    custom_provider_max_retries: int = Field(
        3,
        env="CUSTOM_PROVIDER_MAX_RETRIES"
    )

    # Provider 配置对象
    custom_provider: ProviderConfig = Field(default_factory=ProviderConfig)

    def get_provider_config(self, provider_name: str) -> Optional[ProviderConfig]:
        """获取指定供应商的配置"""
        provider_configs = {
            "openai": self.openai,
            "google_ai": self.google_ai,
            "anthropic": self.anthropic,
            "dashscope": self.dashscope,
            "azure": self.azure,
            "custom_provider": self.custom_provider,  # 添加自定义 provider
        }
        return provider_configs.get(provider_name.lower())
```

## 📦 注册和使用

### 动态注册 Provider

除了在配置中静态注册，还可以动态注册：

```python
from ai_gen_hub.services.provider_manager import ProviderManagerImpl
from your_custom_provider import CustomProvider

async def register_custom_provider():
    """动态注册自定义 provider"""
    # 创建 provider manager
    provider_manager = ProviderManagerImpl()

    # 创建自定义 provider 实例
    config = ProviderConfig(
        api_keys=["your-api-key"],
        base_url="https://api.custom-ai.com/v1",
        enabled=True
    )
    key_manager = KeyManager()
    custom_provider = CustomProvider(config, key_manager)

    # 注册到管理器
    await provider_manager.register_provider(custom_provider)

    print("自定义 provider 注册成功！")
```

### 使用自定义 Provider

```python
from ai_gen_hub.core.interfaces import TextGenerationRequest, Message, MessageRole

async def use_custom_provider():
    """使用自定义 provider 生成文本"""
    # 创建请求
    request = TextGenerationRequest(
        model="custom-model-v1",
        messages=[
            Message(role=MessageRole.USER, content="你好，请介绍一下你的能力")
        ],
        temperature=0.7,
        max_tokens=1000
    )

    # 获取 provider 并生成文本
    provider = await provider_manager.get_provider("custom-model-v1")
    response = await provider.generate_text(request)

    print(response.choices[0].message.content)
```
